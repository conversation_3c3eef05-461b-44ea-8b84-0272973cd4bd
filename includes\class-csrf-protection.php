<?php
/**
 * CSRF Protection Class
 *
 * Provides enhanced CSRF protection for the plugin
 *
 * @package Q-Updater
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class Q_Updater_CSRF_Protection
{
    private $parent;
    private $nonce_lifetime = 86400; // 24 hours by default
    private $token; // CSRF token

    /**
     * Constructor
     *
     * @param Q_Updater $parent Parent class instance
     */
    public function __construct($parent)
    {
        $this->parent = $parent;

        // Add CSRF protection headers to admin pages
        add_action('admin_init', [$this, 'add_csrf_headers']);

        // Initialize CSRF protection
        $this->init();
    }

    /**
     * Initialize CSRF protection
     *
     * @since 1.0.0
     */
    public function init()
    {
        // Add CSRF token to admin footer for JavaScript access
        add_action('admin_footer', [$this, 'output_csrf_token_js']);

        // Add CSRF token to AJAX requests
        add_action('admin_footer', [$this, 'add_csrf_token_to_ajax_requests']);

        // Add refresh token button to admin bar
        add_action('admin_bar_menu', [$this, 'add_refresh_token_button'], 999);

        // Register AJAX handler for refreshing CSRF token
        add_action('wp_ajax_qu_refresh_csrf_token', [$this, 'ajax_refresh_csrf_token']);
    }

    /**
     * AJAX handler for refreshing CSRF token
     */
    public function ajax_refresh_csrf_token()
    {
        // Check nonce
        check_ajax_referer('qu_refresh_csrf_token');

        // Check capability
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'You do not have permission to refresh the CSRF token.']);
            return;
        }

        // Refresh token
        $new_token = $this->refresh_token();

        // Send response
        wp_send_json_success(['token' => $new_token]);
    }

    /**
     * Add refresh token button to admin bar
     *
     * @param WP_Admin_Bar $admin_bar Admin bar object
     */
    public function add_refresh_token_button($admin_bar)
    {
        // Only add on Q-Updater admin pages
        $screen = get_current_screen();
        if (!$screen || strpos($screen->id, 'q-updater') === false) {
            return;
        }

        $admin_bar->add_menu([
            'id' => 'q-updater-refresh-token',
            'title' => 'Refresh CSRF Token',
            'href' => '#',
            'meta' => [
                'title' => 'Refresh CSRF Token',
                'class' => 'qu-refresh-token-button',
                'onclick' => 'qUpdater.refreshToken(); return false;'
            ]
        ]);
    }

    /**
     * Generate CSRF token
     *
     * @since 1.0.0
     */
    private function generate_csrf_token()
    {
        // Generate a random token
        $this->token = bin2hex(random_bytes(32));

        // Store the token in a transient
        set_transient('q_updater_csrf_token', $this->token, DAY_IN_SECONDS);
    }

    /**
     * Get the CSRF token
     *
     * @since 1.0.0
     * @return string The CSRF token
     */
    public function get_token()
    {
        // Get token from transient
        $token = get_transient('q_updater_csrf_token');

        // Generate new token if not found
        if (!$token) {
            $this->generate_csrf_token();
            $token = $this->token;
        }

        return $token;
    }

    /**
     * Output CSRF token for JavaScript
     *
     * @since 1.0.0
     */
    public function output_csrf_token_js()
    {
        // Check if we're on an admin page
        if (!is_admin()) {
            return;
        }

        // Get token
        $token = $this->get_token();

        // Output token for JavaScript
        echo '<script>
            var qUpdater = qUpdater || {};
            qUpdater.csrf_header = "X-CSRF-Token";
            qUpdater.nonce = "' . esc_js($token) . '";

            // Function to refresh the CSRF token
            qUpdater.refreshToken = function() {
                jQuery.ajax({
                    url: ajaxurl,
                    type: "POST",
                    data: {
                        action: "qu_refresh_csrf_token",
                        _wpnonce: "' . wp_create_nonce('qu_refresh_csrf_token') . '"
                    },
                    beforeSend: function(xhr) {
                        xhr.setRequestHeader(qUpdater.csrf_header, qUpdater.nonce);
                    },
                    success: function(response) {
                        if (response.success && response.data.token) {
                            qUpdater.nonce = response.data.token;
                            alert("CSRF token refreshed successfully!");

                            // Update all CSRF token fields in forms
                            jQuery("input[name=\'csrf_token\']").val(qUpdater.nonce);
                        } else {
                            alert("Failed to refresh CSRF token: " + (response.data.message || "Unknown error"));
                        }
                    },
                    error: function() {
                        alert("Failed to refresh CSRF token. Please try again.");
                    }
                });
            };

            // Add CSRF token to all forms
            jQuery(document).ready(function($) {
                $("form").each(function() {
                    if (!$(this).find("input[name=\'csrf_token\']").length) {
                        $(this).append("<input type=\'hidden\' name=\'csrf_token\' value=\'" + qUpdater.nonce + "\'>");
                    }
                });
            });
        </script>';
    }

    /**
     * Add CSRF token to AJAX requests
     *
     * @since 1.0.0
     */
    public function add_csrf_token_to_ajax_requests()
    {
        // Check if we're on an admin page
        if (!is_admin()) {
            return;
        }

        // Output JavaScript to add CSRF token to all AJAX requests
        ?>
        <script>
            jQuery(document).ready(function ($) {
                // Add CSRF token to all AJAX requests
                $.ajaxPrefilter(function (options, originalOptions, jqXHR) {
                    // Only add token to WordPress admin-ajax.php requests
                    if (options.url.indexOf('admin-ajax.php') !== -1) {
                        // Add token as a header
                        if (typeof qUpdater !== 'undefined' && qUpdater.csrf_header && qUpdater.nonce) {
                            jqXHR.setRequestHeader(qUpdater.csrf_header, qUpdater.nonce);
                        }
                    }
                });
            });
        </script>
        <?php
    }

    /**
     * Add CSRF protection headers to admin pages
     */
    public function add_csrf_headers()
    {
        // Check if we're on an admin page
        if (!is_admin()) {
            return;
        }

        // Add security headers
        header('X-Content-Type-Options: nosniff');
        header('X-Frame-Options: SAMEORIGIN');
        header('X-XSS-Protection: 1; mode=block');
        header('Referrer-Policy: strict-origin-when-cross-origin');
        header('Content-Security-Policy: default-src \'self\'; script-src \'self\' \'unsafe-inline\' https://cdn.jsdelivr.net; style-src \'self\' \'unsafe-inline\' https://fonts.googleapis.com; font-src \'self\' data: https://fonts.gstatic.com; img-src \'self\' data: https://secure.gravatar.com;');
    }

    /**
     * Create a nonce with custom lifetime
     *
     * @param string $action Action name
     * @param int $lifetime Nonce lifetime in seconds (default: 24 hours)
     * @return string Nonce
     */
    public function create_nonce($action, $lifetime = null)
    {
        if ($lifetime === null) {
            $lifetime = $this->nonce_lifetime;
        }

        // Add the lifetime to the action to make it unique
        $action = $action . '|' . $lifetime;

        return wp_create_nonce($action);
    }

    /**
     * Verify a nonce with custom lifetime
     *
     * @param string $nonce Nonce to verify
     * @param string $action Action name
     * @param int $lifetime Nonce lifetime in seconds (default: 24 hours)
     * @return bool|int False if nonce is invalid, 1 if nonce is valid, 2 if nonce is valid and generated within 12 hours
     */
    public function verify_nonce($nonce, $action, $lifetime = null)
    {
        if ($lifetime === null) {
            $lifetime = $this->nonce_lifetime;
        }

        // Add the lifetime to the action to make it unique
        $action = $action . '|' . $lifetime;

        return wp_verify_nonce($nonce, $action);
    }

    /**
     * Verify AJAX request with nonce and capability check
     *
     * @param string $nonce_key Nonce key in the request
     * @param string $action Action name
     * @param string $capability Required capability
     * @return bool True if request is valid, false otherwise
     */
    public function verify_ajax_request($nonce_key, $action, $capability)
    {
        // First check for CSRF token in headers
        if ($this->has_valid_csrf_token()) {
            // Token is valid, now check capability
            if (!current_user_can($capability)) {
                return false;
            }
            return true;
        }

        // Fall back to nonce check if token is not present or invalid
        // Check if nonce exists
        if (!isset($_REQUEST[$nonce_key])) {
            // For backward compatibility, try standard WordPress nonce verification
            if (check_ajax_referer($action, $nonce_key, false)) {
                // Nonce is valid, now check capability
                if (!current_user_can($capability)) {
                    return false;
                }
                return true;
            }
            return false;
        }

        // Verify nonce
        $nonce = sanitize_text_field($_REQUEST[$nonce_key]);

        // First try our custom verification
        if ($this->verify_nonce($nonce, $action)) {
            // Nonce is valid, now check capability
            if (!current_user_can($capability)) {
                return false;
            }
            return true;
        }

        // If that fails, try standard WordPress nonce verification
        if (wp_verify_nonce($nonce, $action)) {
            // Nonce is valid, now check capability
            if (!current_user_can($capability)) {
                return false;
            }
            return true;
        }

        return false;
    }

    /**
     * Verify form submission with nonce and capability check
     *
     * @param string $nonce_key Nonce key in the request
     * @param string $action Action name
     * @param string $capability Required capability
     * @return bool True if request is valid, false otherwise
     */
    public function verify_form_submission($nonce_key, $action, $capability)
    {
        // First check for CSRF token in headers
        if ($this->has_valid_csrf_token()) {
            // Token is valid, now check capability
            if (!current_user_can($capability)) {
                return false;
            }
            return true;
        }

        // Fall back to nonce check if token is not present or invalid
        // Check if nonce exists
        if (!isset($_POST[$nonce_key])) {
            return false;
        }

        // Verify nonce
        $nonce = sanitize_text_field($_POST[$nonce_key]);
        if (!$this->verify_nonce($nonce, $action)) {
            return false;
        }

        // Check capability
        if (!current_user_can($capability)) {
            return false;
        }

        return true;
    }

    /**
     * Get nonce field HTML
     *
     * @param string $action Action name
     * @param string $name Nonce name
     * @param bool $referer Whether to include the referer field
     * @param int $lifetime Nonce lifetime in seconds (default: 24 hours)
     * @return string Nonce field HTML
     */
    public function nonce_field($action, $name = '_wpnonce', $referer = true, $lifetime = null)
    {
        if ($lifetime === null) {
            $lifetime = $this->nonce_lifetime;
        }

        // Add the lifetime to the action to make it unique
        $action = $action . '|' . $lifetime;

        return wp_nonce_field($action, $name, $referer, false);
    }

    /**
     * Get nonce URL
     *
     * @param string $actionurl URL to add nonce to
     * @param string $action Action name
     * @param string $name Nonce name
     * @param int $lifetime Nonce lifetime in seconds (default: 24 hours)
     * @return string URL with nonce added
     */
    public function nonce_url($actionurl, $action, $name = '_wpnonce', $lifetime = null)
    {
        if ($lifetime === null) {
            $lifetime = $this->nonce_lifetime;
        }

        // Add the lifetime to the action to make it unique
        $action = $action . '|' . $lifetime;

        return wp_nonce_url($actionurl, $action, $name);
    }

    /**
     * Verify CSRF token
     *
     * @param string $token Token to verify
     * @return bool True if token is valid, false otherwise
     */
    public function verify_csrf_token($token)
    {
        if (empty($token)) {
            return false;
        }

        $stored_token = get_transient('q_updater_csrf_token');

        if (empty($stored_token)) {
            return false;
        }

        return $token === $stored_token;
    }

    /**
     * Get CSRF token from request headers
     *
     * @return string|false CSRF token or false if not found
     */
    public function get_csrf_token_from_headers()
    {
        $headers = getallheaders();

        if (isset($headers['X-CSRF-Token'])) {
            return $headers['X-CSRF-Token'];
        }

        return false;
    }

    /**
     * Get CSRF token from request body
     *
     * @return string|false CSRF token or false if not found
     */
    public function get_csrf_token_from_request()
    {
        if (isset($_REQUEST['csrf_token'])) {
            return sanitize_text_field($_REQUEST['csrf_token']);
        }

        return false;
    }

    /**
     * Check if request has valid CSRF token
     *
     * @return bool True if request has valid CSRF token, false otherwise
     */
    public function has_valid_csrf_token()
    {
        // First check headers
        $token = $this->get_csrf_token_from_headers();

        if ($token && $this->verify_csrf_token($token)) {
            return true;
        }

        // Then check request body
        $token = $this->get_csrf_token_from_request();

        if ($token && $this->verify_csrf_token($token)) {
            return true;
        }

        return false;
    }

    /**
     * Refresh CSRF token
     *
     * @return string New CSRF token
     */
    public function refresh_token()
    {
        // Delete the old token
        delete_transient('q_updater_csrf_token');

        // Generate a new token
        $this->generate_csrf_token();

        return $this->token;
    }

    /**
     * Add CSRF token to URL
     *
     * @param string $url URL to add token to
     * @return string URL with token added
     */
    public function add_token_to_url($url)
    {
        $token = $this->get_token();
        $separator = (strpos($url, '?') !== false) ? '&' : '?';

        return $url . $separator . 'csrf_token=' . urlencode($token);
    }
}
